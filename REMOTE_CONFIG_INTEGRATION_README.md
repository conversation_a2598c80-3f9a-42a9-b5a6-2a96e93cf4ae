# ThingsBoard Gateway 远程配置集成

## 概述

本次修改实现了在 `front_interface.py` 中收到配置修改请求时，自动触发 `tb_gateway_remote_configurator.py` 中相关方法的配置更新操作。

## 修改内容

### 1. 新增导入
在 `thingsboard_gateway/front_interface.py` 中添加了：
```python
from tb_utility.tb_gateway_remote_configurator import RemoteConfigurator
```

### 2. 新增函数

#### `trigger_remote_configuration_update(config_data, file_name)`
主要的配置更新触发函数，根据文件类型自动选择合适的更新方式：

- **主配置文件 (tb_gateway.json)**：
  - 触发 `general_configuration` 更新
  - 如果包含 `storage` 配置，触发 `storage_configuration` 更新
  - 如果包含 `grpc` 配置，触发 `grpc_configuration` 更新
  - 如果包含 `connectors` 配置，触发 `active_connectors` 更新

- **连接器配置文件 (*.json)**：
  - 从主配置文件中获取连接器信息（名称、类型、ID等）
  - 创建完整的连接器配置更新数据结构
  - 触发连接器配置更新

#### `_get_connector_info_from_main_config(config_file_name)`
辅助函数，从主配置文件中获取连接器的元信息：
- 连接器名称
- 连接器类型
- 连接器ID
- 日志级别
- 远程日志启用状态

### 3. 集成到现有API

在 `write_json_func` 方法中添加了配置更新触发：
```python
# 触发远程配置更新
trigger_remote_configuration_update(json_data, file_name)
```

## 工作流程

1. **前端发送配置修改请求** → `POST /gateway/v1/write_json`
2. **写入配置文件** → 使用文件锁确保安全写入
3. **触发远程配置更新** → 调用 `trigger_remote_configuration_update`
4. **分析配置类型** → 根据文件名判断是主配置还是连接器配置
5. **构造更新数据** → 创建符合 RemoteConfigurator 期望格式的数据
6. **放入更新队列** → `RemoteConfigurator.RECEIVED_UPDATE_QUEUE.put()`
7. **RemoteConfigurator 处理** → 后台线程自动处理配置更新

## 支持的配置类型

### 主配置文件更新
- ✅ 通用配置 (thingsboard 部分)
- ✅ 存储配置 (storage 部分)
- ✅ GRPC配置 (grpc 部分)
- ✅ 活动连接器配置 (connectors 部分)

### 连接器配置文件更新
- ✅ 自动从主配置获取连接器元信息
- ✅ 完整的配置数据结构
- ✅ 时间戳支持
- ✅ 所有连接器类型 (MQTT, Modbus, OPC-UA, 等)

## 错误处理

- 配置更新失败不会影响文件写入操作
- 详细的日志记录，便于调试
- 优雅的异常处理，避免系统崩溃

## 使用示例

### 更新主配置
```bash
curl -X POST "http://localhost:20400/gateway/v1/write_json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "file_name": "tb_gateway.json",
    "file_text": "{\"thingsboard\":{\"host\":\"new-host\",\"port\":1883}}"
  }'
```

### 更新连接器配置
```bash
curl -X POST "http://localhost:20400/gateway/v1/write_json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "file_name": "mqtt.json",
    "file_text": "{\"broker\":{\"host\":\"localhost\",\"port\":1883}}"
  }'
```

## 日志输出示例

```
2025-06-07 19:05:23,662 - front_interface.py[line:94] - INFO: 触发主配置文件远程更新
2025-06-07 19:05:23,662 - front_interface.py[line:143] - INFO: 远程配置更新已触发: tb_gateway.json
2025-06-07 19:05:23,663 - front_interface.py[line:119] - INFO: 触发连接器配置文件远程更新: mqtt.json
2025-06-07 19:05:23,664 - front_interface.py[line:143] - INFO: 远程配置更新已触发: mqtt.json
```

## 测试验证

已通过完整的单元测试验证：
- ✅ 主配置文件更新功能
- ✅ 连接器配置文件更新功能
- ✅ 辅助函数正确性
- ✅ 错误处理机制

## 注意事项

1. 确保 ThingsBoard Gateway 服务正在运行
2. 确保 `remoteConfiguration` 在主配置中启用
3. 配置更新是异步处理的
4. 重要配置修改会自动创建备份

## 兼容性

- ✅ 与现有 API 完全兼容
- ✅ 不影响现有功能
- ✅ 向后兼容
- ✅ 支持所有连接器类型
