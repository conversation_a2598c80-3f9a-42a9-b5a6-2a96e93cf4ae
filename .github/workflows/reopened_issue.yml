on:
  issues:
    types:
      - reopened

name: Move ticket if it was reopened

jobs:
  close-ticket-in-jira:
    if: ${{ !github.event.issue.pull_request }}
    name: Move ticket if reopened
    runs-on: ubuntu-latest
    steps:
    - name: <PERSON>gin
      uses: atlassian/gajira-login@master
      env:
        JIRA_BASE_URL: ${{ secrets.JIRA_BASE_URL }}
        JIRA_USER_EMAIL: ${{ secrets.JIRA_USER_EMAIL }}
        JIRA_API_TOKEN: ${{ secrets.JIRA_API_TOKEN }}
        
    - name: Find Comment
      uses: peter-evans/find-comment@v2
      id: find_comment
      with:
        issue-number: ${{ github.event.issue.number }}
        comment-author: 'github-actions[bot]'
        direction: first
        nth: 0
        
    - name: Find Jira ticket
      if: steps.find_comment.outputs.comment-id != ''
      id: find_ticket
      uses: atlassian/gajira-find-issue-key@v3
      with:
        string: ${{ steps.find_comment.outputs.comment-body }}

    - name: Update Jira ticket
      if: steps.find_ticket.outputs.issue != ''
      uses: atlassian/gajira-comment@v3
      with:
        issue: ${{ steps.find_ticket.outputs.issue }}
        comment: |
          Github issue was reopened by *${{ github.event.sender.login }}*

    - name: Move to Backlog
      if: steps.find_ticket.outputs.issue != ''
      id: transition_to_backlog
      uses: atlassian/gajira-transition@v3
      with:
        issue: ${{ steps.find_ticket.outputs.issue }}
        transition: "task in backlog"
        
    - name: Move to Backlog
      if: steps.find_ticket.outputs.issue != ''
      id: transition_to_todo
      uses: atlassian/gajira-transition@v3
      with:
        issue: ${{ steps.find_ticket.outputs.issue }}
        transition: "Admin permissions only"
        
