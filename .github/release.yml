#     Copyright 2025. ThingsBoard
#
#     Licensed under the Apache License, Version 2.0 (the "License");
#     you may not use this file except in compliance with the License.
#     You may obtain a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#     Unless required by applicable law or agreed to in writing, software
#     distributed under the License is distributed on an "AS IS" BASIS,
#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#     See the License for the specific language governing permissions and
#     limitations under the License.

exclude-labels:
  - 'Ignore for release'
categories:
  - title: '📌 Major improvements'
    labels:
      - 'Major'
  - title: '⚛ Core improvements'
    labels:
      - 'Core'
  - title: '🔗 Connectors improvements'
    labels:
      - 'Connectors'
      - 'Connector'
      - 'Modbus'
      - 'MQTT'
      - 'BACnet'
      - 'BLE'
      - 'CAN'
      - 'FTP'
      - 'KNX'
      - 'OCPP'
      - 'ODBC'
      - 'OPC-UA'
      - 'Request'
      - 'REST'
      - 'SNMP'
      - 'Socket'
      - 'XMPP'
  - title: '🗄 Storages improvements'
    labels:
      - 'Storages'
      - 'Storage'
      - 'Memory'
      - 'File'
      - 'SQLite'
  - title: '🐛 Bug fixes'
    labels:
      - 'Bug'
  - title: 'Other'
    labels:
      - 'Other'
      - 'Tests'
template: |
  ## What's Changed
  $CHANGES