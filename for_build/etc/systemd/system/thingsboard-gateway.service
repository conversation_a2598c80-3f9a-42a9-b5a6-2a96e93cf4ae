[Unit]
Description=ThingsBoard Gateway
After=multi-user.target
ConditionPathExists=/etc/thingsboard-gateway/config/tb_gateway.json

[Service]
Type=simple
User=thingsboard_gateway
Group=thingsboard_gateway

WorkingDirectory=/var/lib/thingsboard_gateway
ExecStart=/var/lib/thingsboard_gateway/venv/bin/python3 -c "from thingsboard_gateway.tb_gateway import daemon; daemon()"
ExecStop=/bin/kill -INT $MAINPID
ExecReload=/bin/kill -TERM $MAINPID

Restart=on-failure

[Install]
WantedBy=multi-user.target
