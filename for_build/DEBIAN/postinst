#!/bin/sh
set -e

echo "Postinst: Ensuring required directories exist..."
for dir in /var/lib/thingsboard_gateway /etc/thingsboard-gateway /var/log/thingsboard-gateway; do
    if [ ! -d "$dir" ]; then
        echo "Postinst: Creating $dir..."
        mkdir -p "$dir"
    fi
done

# Extract configuration files from configs.tar.gz if present
if [ -f /etc/thingsboard-gateway/configs.tar.gz ]; then
    if [ -d /etc/thingsboard-gateway/config ]; then
        echo "Postinst: Directory /etc/thingsboard-gateway/config exists. Creating backup as configs_backup.tar.gz..."
        tar -czf /etc/thingsboard-gateway/configs_backup.tar.gz -C /etc/thingsboard-gateway config
        echo "Postinst: Removing existing /etc/thingsboard-gateway/config directory..."
        rm -rf /etc/thingsboard-gateway/config
    fi
    echo "Postinst: Extracting configuration files from configs.tar.gz..."
    tar -xzf /etc/thingsboard-gateway/configs.tar.gz -C /etc/thingsboard-gateway
    rm -f /etc/thingsboard-gateway/configs.tar.gz
fi

# Extract extensions archive into /var/lib/thingsboard_gateway/extensions
if [ -f /var/lib/thingsboard_gateway/extensions.tar.gz ]; then
    if [ -d /var/lib/thingsboard_gateway/extensions ]; then
        echo "Postinst: Directory /var/lib/thingsboard_gateway/extensions exists. Creating backup as extensions_backup.tar.gz..."
        tar -czf /var/lib/thingsboard_gateway/extensions_backup.tar.gz -C /var/lib/thingsboard_gateway extensions
        echo "Postinst: Removing existing /var/lib/thingsboard_gateway/extensions directory..."
        rm -rf /var/lib/thingsboard_gateway/extensions
    fi
    echo "Postinst: Extracting extensions from extensions.tar.gz..."
    rm -rf /var/lib/thingsboard_gateway/extensions
    mkdir -p /var/lib/thingsboard_gateway/extensions
    tar -xzf /var/lib/thingsboard_gateway/extensions.tar.gz -C /var/lib/thingsboard_gateway/extensions
    rm -f /var/lib/thingsboard_gateway/extensions.tar.gz
fi

VENV_PATH="/var/lib/thingsboard_gateway/venv"

echo "Postinst: Setting up Python virtual environment at $VENV_PATH..."
if [ -d "$VENV_PATH" ]; then
    echo "Postinst: Removing existing virtual environment..."
    rm -rf "$VENV_PATH"
fi
python3 -m venv "$VENV_PATH"
"$VENV_PATH/bin/pip" install --upgrade pip setuptools

# Install the locally built wheel from /var/lib/thingsboard_gateway.
WHEEL_FILE=$(ls /var/lib/thingsboard_gateway/*.whl 2>/dev/null || true)
if [ -z "$WHEEL_FILE" ]; then
    echo "Postinst Error: No wheel file found in /var/lib/thingsboard_gateway." >&2
    exit 1
fi
echo "Postinst: Installing wheel package: $WHEEL_FILE into the virtual environment..."
"$VENV_PATH/bin/pip" install --upgrade --force-reinstall "$WHEEL_FILE"
rm -f /var/lib/thingsboard_gateway/$(basename "$WHEEL_FILE")

echo "Postinst: Setting ownership for directories..."
chown -R thingsboard_gateway:thingsboard_gateway /var/lib/thingsboard_gateway
chown -R thingsboard_gateway:thingsboard_gateway /etc/thingsboard-gateway
chown -R thingsboard_gateway:thingsboard_gateway /var/log/thingsboard-gateway

echo "Postinst: Enabling thingsboard-gateway service..."
systemctl enable thingsboard-gateway.service
echo "Postinst: Starting thingsboard-gateway service..."
systemctl start thingsboard-gateway.service
echo ""
echo -e "Postinst: \e[92mThingsBoard IoT Gateway has been installed successfully.\e[39m"
echo -e "Have a nice day! \e[95m\e[5m:)\e[25m\e[39m" || echo "Have a nice day!"
exit 0
