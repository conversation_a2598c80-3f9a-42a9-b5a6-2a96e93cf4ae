{"Opcua": {"name": "Opcua", "type": "opcua_asyncio", "logLevel": "DEBUG", "configuration": "opcua.json", "configurationJson": {"server": {"name": "OPC-UA Demo Server", "url": "opc.tcp://127.0.0.1:4840/freeopcua/server/", "timeoutInMillis": 5000, "scanPeriodInMillis": 1000, "disableSubscriptions": true, "subCheckPeriodInMillis": 100, "showMap": false, "security": "Basic128Rsa15", "identity": {"type": "anonymous"}, "mapping": [{"deviceNodePattern": "Root\\.Objects\\.TempSensor", "deviceNamePattern": "Temp Sensor", "deviceTypePattern": "default", "attributes": [], "timeseries": [{"key": "path", "path": "${Humidity}"}], "rpc_methods": [{"method": "multiply", "arguments": [2, 4]}], "attributes_updates": []}]}}}}