{"connection": {"str": "DO_NOT_EDIT_THIS_PARAMETER.IT WILL BE OVERRIDDEN BY TESTS.", "attributes": {"autocommit": false, "timeout": 0}}, "pyodbc": {"pooling": false, "native_uuid": true}, "polling": {"query": "SELECT bool_v, str_v, dbl_v, long_v, device_id, ts FROM timeseries WHERE ts > ? ORDER BY ts ASC LIMIT 10", "period": 1, "iterator": {"column": "ts", "query": "SELECT MIN(ts) - 1 FROM timeseries"}}, "mapping": {"device": {"type": "postgres", "name": "'ODBC ' + str(device_id)"}, "timeseries": [{"name": "value", "value": "[i for i in [str_v, long_v, dbl_v,bool_v] if i is not None][0]"}]}}