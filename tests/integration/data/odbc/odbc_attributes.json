{"connection": {"str": "DO_NOT_EDIT_THIS_PARAMETER.IT WILL BE OVERRIDDEN BY TESTS."}, "polling": {"query": "SELECT key, bool_v, str_v, dbl_v, long_v, device_id, ts FROM attributes WHERE ts > ? ORDER BY ts ASC LIMIT 10", "period": 1, "iterator": {"column": "ts", "query": "SELECT MIN(ts) - 1 FROM attributes"}}, "mapping": {"device": {"name": "'ODBC ' + str(device_id)"}, "attributes": [{"nameExpression": "key", "value": "[i for i in [str_v, long_v, dbl_v,bool_v] if i is not None][0]"}]}}