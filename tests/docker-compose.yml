version: '3.5'
services:
  tb:
    container_name: tests_tb_1
    image: "thingsboard/tb-postgres"
    environment:
      TB_QUEUE_TYPE: in-memory
    volumes:
      - tb-data:/data
      - tb-logs:/var/log/thingsboard
    network_mode: host
  gw:
    container_name: tests_gw_1
    image: "tb-gateway"
    environment:
      - TB_GW_HOST=127.0.0.1
      - TB_GW_PORT=1883
      - TB_GW_ACCESS_TOKEN=YOUR_ACCESS_TOKEN
      - TB_GW_RATE_LIMITS=0:0
    volumes:
      - tb-gw-config:/thingsboard_gateway/config
      - tb-gw-logs:/thingsboard_gateway/logs
      - tb-gw-extensions:/thingsboard_gateway/extensions
    network_mode: host
  mqtt-broker:
    container_name: tests_mqtt_broker_1
    image: "thingsboard/tb-gw-mqtt-broker:latest"
    network_mode: host
  modbus-server:
    container_name: tests_modbus_server_1
    image: "thingsboard/tb-gw-modbus-server:latest"
    network_mode: host
  opcua-server:
    container_name: tests_opcua_server_1
    image: "thingsboard/tb-gw-opcua-server:latest"
    network_mode: host
volumes:
  tb-data:
    name: tb-data
  tb-logs:
    name: tb-logs
  tb-gw-config:
    name: tb-gw-config
  tb-gw-logs:
    name: tb-gw-logs
  tb-gw-extensions:
    name: tb-gw-extensions
