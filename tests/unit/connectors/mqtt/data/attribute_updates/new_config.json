{"broker": {"name": "Default Local Broker", "host": "127.0.0.1", "port": 1883, "clientId": "ThingsBoard_gateway", "version": 5, "maxMessageNumberPerWorker": 10, "maxNumberOfWorkers": 100, "sendDataOnlyOnChange": false, "security": {"type": "anonymous"}}, "mapping": [], "requestsMapping": {"serverSideRpc": {}, "connectRequests": {}, "disconnectRequests": {}, "attributeRequests": {}, "attributeUpdates": [{"retain": true, "qos": 0, "deviceNameFilter": ".*", "attributeFilter": "uploadFrequency", "topicExpression": "sensor/${deviceName}/${attributeKey}", "valueExpression": "{\"${attributeKey}\":\"${attributeValue}\"}"}]}}