{"broker": {"name": "Default Local Broker", "host": "127.0.0.1", "port": 1883, "clientId": "ThingsBoard_gateway", "version": 5, "maxMessageNumberPerWorker": 10, "maxNumberOfWorkers": 100, "sendDataOnlyOnChange": false, "security": {"type": "anonymous"}}, "mapping": [], "requestsMapping": {"serverSideRpc": {}, "connectRequests": {}, "disconnectRequests": [{"topicFilter": "sensor/disconnect", "deviceInfo": {"deviceNameExpressionSource": "message", "deviceNameExpression": "${serialNumber}"}}, {"topicFilter": "sensor/+/disconnect", "deviceInfo": {"deviceNameExpressionSource": "topic", "deviceNameExpression": "(?<=sensor/)(.*?)(?=/disconnect)"}}], "attributeRequests": {}, "attributeUpdates": {}}}