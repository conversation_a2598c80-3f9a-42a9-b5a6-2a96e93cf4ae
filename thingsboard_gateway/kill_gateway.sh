#!/bin/bash

# 定义进程名
process_name="thingsboard_gateway/tb_gateway.py"

# 查找进程ID
get_process_id() {
    echo "正在查找进程ID..."
    pid=$(pgrep -f "$process_name")
    if [ -z "$pid" ]; then
        echo "未找到进程 $process_name"
        return 1
    else
        echo "找到进程ID: $pid"
        return 0
    fi
}

# 杀掉进程
kill_process() {
    if get_process_id; then
        echo "正在杀掉进程: $process_name (PID: $pid)"
        kill $pid
    else
        echo "未找到进程，无需杀掉。"
    fi
}

# 执行杀掉进程的操作
echo "++++++++++++++"
kill_process
echo "进程杀掉完成。"
echo "--------------"