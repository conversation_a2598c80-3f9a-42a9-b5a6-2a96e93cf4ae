#!/bin/bash

# 优化的网关进程终止脚本
# 支持优雅关闭、强制终止、子进程处理等

# 定义进程名和超时时间
process_name="thingsboard_gateway/tb_gateway.py"
GRACEFUL_TIMEOUT=10  # 优雅关闭超时时间（秒）
FORCE_TIMEOUT=5      # 强制终止超时时间（秒）

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1"
}

log_warn() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] WARN: $1"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1"
}

# 查找所有相关进程ID（包括子进程）
get_process_ids() {
    # 查找主进程
    main_pids=$(pgrep -f "$process_name")

    # 查找可能的子进程（Python相关）
    child_pids=$(pgrep -f "python.*gateway" | grep -v $$)

    # 合并并去重
    all_pids=$(echo "$main_pids $child_pids" | tr ' ' '\n' | sort -u | tr '\n' ' ')

    echo "$all_pids"
}

# 检查进程是否存在
is_process_running() {
    local pid=$1
    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 等待进程终止
wait_for_process_termination() {
    local pids="$1"
    local timeout=$2
    local count=0

    while [ $count -lt $timeout ]; do
        local running_pids=""
        for pid in $pids; do
            if is_process_running "$pid"; then
                running_pids="$running_pids $pid"
            fi
        done

        if [ -z "$running_pids" ]; then
            return 0  # 所有进程都已终止
        fi

        sleep 1
        count=$((count + 1))
    done

    return 1  # 超时
}

# 优雅关闭进程
graceful_shutdown() {
    local pids="$1"

    if [ -z "$pids" ]; then
        log_info "未找到需要关闭的进程"
        return 0
    fi

    log_info "开始优雅关闭进程: $pids"

    # 发送 SIGTERM 信号
    for pid in $pids; do
        if is_process_running "$pid"; then
            log_info "向进程 $pid 发送 SIGTERM 信号"
            kill -TERM "$pid" 2>/dev/null || log_warn "无法向进程 $pid 发送 SIGTERM 信号"
        fi
    done

    # 等待进程优雅关闭
    if wait_for_process_termination "$pids" $GRACEFUL_TIMEOUT; then
        log_info "所有进程已优雅关闭"
        return 0
    else
        log_warn "优雅关闭超时，部分进程仍在运行"
        return 1
    fi
}

# 强制终止进程
force_kill() {
    local pids="$1"

    if [ -z "$pids" ]; then
        return 0
    fi

    log_warn "开始强制终止进程: $pids"

    # 发送 SIGKILL 信号
    for pid in $pids; do
        if is_process_running "$pid"; then
            log_warn "强制终止进程 $pid"
            kill -KILL "$pid" 2>/dev/null || log_error "无法强制终止进程 $pid"
        fi
    done

    # 等待进程强制终止
    if wait_for_process_termination "$pids" $FORCE_TIMEOUT; then
        log_info "所有进程已强制终止"
        return 0
    else
        log_error "强制终止失败，部分进程可能仍在运行"
        return 1
    fi
}

# 主要的进程终止函数
kill_gateway_processes() {
    log_info "开始终止网关进程..."

    # 获取所有相关进程ID
    pids=$(get_process_ids)

    if [ -z "$pids" ]; then
        log_info "未找到网关进程，无需终止"
        return 0
    fi

    log_info "找到进程: $pids"

    # 首先尝试优雅关闭
    if graceful_shutdown "$pids"; then
        log_info "网关进程已成功关闭"
        return 0
    fi

    # 如果优雅关闭失败，获取仍在运行的进程
    running_pids=""
    for pid in $pids; do
        if is_process_running "$pid"; then
            running_pids="$running_pids $pid"
        fi
    done

    # 强制终止仍在运行的进程
    if [ -n "$running_pids" ]; then
        if force_kill "$running_pids"; then
            log_info "网关进程已强制终止"
            return 0
        else
            log_error "网关进程终止失败"
            return 1
        fi
    fi

    log_info "网关进程终止完成"
    return 0
}

# 执行主函数
echo "=============================================="
log_info "开始执行网关进程终止脚本"
echo "=============================================="

if kill_gateway_processes; then
    log_info "网关进程终止脚本执行成功"
    exit 0
else
    log_error "网关进程终止脚本执行失败"
    exit 1
fi