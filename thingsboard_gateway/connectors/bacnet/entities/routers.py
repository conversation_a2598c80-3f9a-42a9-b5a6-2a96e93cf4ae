#     Copyright 2025. ThingsBoard
#
#     Licensed under the Apache License, Version 2.0 (the "License");
#     you may not use this file except in compliance with the License.
#     You may obtain a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#     Unless required by applicable law or agreed to in writing, software
#     distributed under the License is distributed on an "AS IS" BASIS,
#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#     See the License for the specific language governing permissions and
#     limitations under the License.

from asyncio import Lock


class Routers:
    def __init__(self):
        self.__lock = Lock()
        self.__router_info_cache = {}

    async def add_router_info(self, address, info):
        await self.__lock.acquire()
        try:
            if address not in self.__router_info_cache:
                self.__router_info_cache[address] = info
        finally:
            self.__lock.release()

    async def get_router_info_by_address(self, address):
        await self.__lock.acquire()
        try:
            return self.__router_info_cache.get(address)
        finally:
            self.__lock.release()
