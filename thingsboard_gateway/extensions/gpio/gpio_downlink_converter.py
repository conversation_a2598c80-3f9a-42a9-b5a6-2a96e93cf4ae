#     Copyright 2024. ThingsBoard
#
#     Licensed under the Apache License, Version 2.0 (the "License");
#     you may not use this file except in compliance with the License.
#     You may obtain a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#     Unless required by applicable law or agreed to in writing, software
#     distributed under the License is distributed on an "AS IS" BASIS,
#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#     See the License for the specific language governing permissions and
#     limitations under the License.

from thingsboard_gateway.connectors.converter import Converter


class GpioDownlinkConverter(Converter):
    """
    GPIO Downlink converter is used to convert data from platform to GPIO format.
    This converter handles RPC commands and attribute updates for GPIO control.
    """

    def __init__(self, config, logger):
        self._log = logger
        self.__config = config

    def convert(self, config, data):
        """
        Convert platform data to GPIO pin control format.
        
        Args:
            config: RPC/attribute configuration
            data: Data from platform (parameters for RPC or attribute values)
            
        Returns:
            dict: Dictionary with pin numbers as keys and values as GPIO states
        """
        self._log.debug("GPIO downlink data to convert: %s", data)
        
        try:
            gpio_commands = {}
            
            # Handle different data formats
            if isinstance(data, dict):
                # Direct pin control: {"pin": 17, "value": 1} or {"17": 1, "18": 0}
                if "pin" in data and "value" in data:
                    pin = int(data["pin"])
                    value = int(data["value"])
                    gpio_commands[pin] = value
                else:
                    # Multiple pin control
                    for key, value in data.items():
                        try:
                            pin = int(key) if str(key).isdigit() else self.__get_pin_from_config(key, config)
                            if pin is not None:
                                gpio_commands[pin] = int(value)
                        except (ValueError, TypeError) as e:
                            self._log.warning("Invalid GPIO pin/value pair: %s=%s, error: %s", key, value, e)
            
            elif isinstance(data, (int, str)):
                # Single value for configured pin
                pin = config.get("pin") if config else None
                if pin is not None:
                    gpio_commands[int(pin)] = int(data)
                else:
                    self._log.error("No pin specified in config for single value: %s", data)
            
            else:
                self._log.error("Unsupported GPIO downlink data format: %s", type(data))
                return {}
            
            # Validate GPIO commands
            validated_commands = self.__validate_gpio_commands(gpio_commands, config)
            
            self._log.debug("Converted GPIO downlink commands: %s", validated_commands)
            return validated_commands
            
        except Exception as e:
            self._log.error("Error converting GPIO downlink data: %s", e)
            return {}

    def __get_pin_from_config(self, key, config):
        """Get GPIO pin number from configuration based on key"""
        if config and "pinMapping" in config:
            return config["pinMapping"].get(key)
        return None

    def __validate_gpio_commands(self, commands, config):
        """Validate and filter GPIO commands"""
        validated = {}
        
        # Get allowed pins from config
        allowed_pins = None
        if config and "allowedPins" in config:
            allowed_pins = config["allowedPins"]
        elif self.__config and "gpioPins" in self.__config:
            allowed_pins = self.__config["gpioPins"]
        
        for pin, value in commands.items():
            try:
                # Validate pin number
                pin_num = int(pin)
                if pin_num < 0 or pin_num > 40:  # Typical GPIO pin range
                    self._log.warning("GPIO pin %s out of range (0-40)", pin_num)
                    continue
                
                # Check if pin is allowed
                if allowed_pins is not None and pin_num not in allowed_pins:
                    self._log.warning("GPIO pin %s not in allowed pins list: %s", pin_num, allowed_pins)
                    continue
                
                # Validate value (0 or 1)
                gpio_value = int(value)
                if gpio_value not in [0, 1]:
                    self._log.warning("Invalid GPIO value %s for pin %s (must be 0 or 1)", gpio_value, pin_num)
                    continue
                
                validated[pin_num] = gpio_value
                
            except (ValueError, TypeError) as e:
                self._log.warning("Invalid GPIO pin/value: %s=%s, error: %s", pin, value, e)
        
        return validated


# Backward compatibility aliases
CustomGpioDownlinkConverter = GpioDownlinkConverter
GpioDownlinkCoverter = GpioDownlinkConverter 