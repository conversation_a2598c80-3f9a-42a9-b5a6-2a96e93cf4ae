#     Copyright 2024. ThingsBoard
#
#     Licensed under the Apache License, Version 2.0 (the "License");
#     you may not use this file except in compliance with the License.
#     You may obtain a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#     Unless required by applicable law or agreed to in writing, software
#     distributed under the License is distributed on an "AS IS" BASIS,
#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#     See the License for the specific language governing permissions and
#     limitations under the License.

from typing import Any, Tuple

from thingsboard_gateway.connectors.converter import Converter
from thingsboard_gateway.gateway.constants import REPORT_STRATEGY_PARAMETER, TELEMETRY_PARAMETER, TIMESERIES_PARAMETER
from thingsboard_gateway.gateway.entities.converted_data import ConvertedData
from thingsboard_gateway.gateway.entities.datapoint_key import DatapointKey
from thingsboard_gateway.gateway.entities.report_strategy_config import ReportStrategyConfig
from thingsboard_gateway.gateway.entities.telemetry_entry import TelemetryEntry
from thingsboard_gateway.tb_utility.tb_utility import TBUtility


class GpioUplinkConverter(Converter):
    """
    GPIO Uplink converter is used to convert incoming GPIO data to the format that platform expects.
    This converter handles GPIO pin states and converts them to telemetry and attributes.
    """

    def __init__(self, config, logger):
        self._log = logger
        self.__config = config
        self.__device_report_strategy = None
        self.__device_name = self.__config.get('deviceName', self.__config.get('name', 'GpioDevice'))
        self.__device_type = self.__config.get('deviceType', self.__config.get('type', 'default'))
        try:
            self.__device_report_strategy = ReportStrategyConfig(self.__config.get(REPORT_STRATEGY_PARAMETER))
        except ValueError as e:
            self._log.trace("Report strategy config is not specified for device %s: %s", self.__device_name, e)

    def convert(self, config, data):
        """Converts incoming GPIO data to the format that platform expects."""
        self._log.debug("GPIO data to convert: %s", data)
        
        if config is not None:
            # For RPC responses
            converted_data = {"result": self.__convert_value_to_type(data, config)}
            return converted_data
        else:
            # For normal telemetry/attributes
            converted_data = ConvertedData(self.__device_name, self.__device_type)
            
            # Process telemetry data
            for datapoint_config in self.__config.get(TIMESERIES_PARAMETER, self.__config.get(TELEMETRY_PARAMETER, [])):
                try:
                    telemetry_entry = self.__convert_telemetry_datapoint(data, datapoint_config)
                    if telemetry_entry:
                        converted_data.add_to_telemetry(telemetry_entry)
                except Exception as e:
                    self._log.error("Error converting telemetry datapoint: %s", e)
            
            # Process attributes data
            for datapoint_config in self.__config.get('attributes', []):
                try:
                    attribute_data = self.__convert_attributes_datapoint(data, datapoint_config)
                    if attribute_data:
                        converted_data.add_to_attributes(*attribute_data)
                except Exception as e:
                    self._log.error("Error converting attribute datapoint: %s", e)
            
            self._log.debug("Converted GPIO data: %s", converted_data)
            return converted_data

    def __convert_telemetry_datapoint(self, data, dp_config) -> TelemetryEntry:
        """Convert GPIO data to telemetry datapoint"""
        key = dp_config.get('key')
        datapoint_key = self.__convert_datapoint_key(key, dp_config, self.__device_report_strategy, self._log)
        value = self.__convert_gpio_value_to_type(data, dp_config)
        
        if datapoint_key is None or value is None:
            self._log.trace("Datapoint %s - not found in incoming GPIO data: %s", key, data)
            return None
        
        return TelemetryEntry({datapoint_key: value})

    def __convert_attributes_datapoint(self, data, dp_config) -> Tuple[DatapointKey, Any]:
        """Convert GPIO data to attributes datapoint"""
        key = dp_config.get('key')
        datapoint_key = self.__convert_datapoint_key(key, dp_config, self.__device_report_strategy, self._log)
        value = self.__convert_gpio_value_to_type(data, dp_config)
        
        if datapoint_key is None or value is None:
            self._log.trace("Datapoint %s - not found in incoming GPIO data: %s", key, data)
            return None
        
        return (datapoint_key, value)

    def __convert_gpio_value_to_type(self, data, dp_config):
        """Convert GPIO data to specified type"""
        try:
            pin = dp_config.get('pin')
            data_type = dp_config.get('type', 'int')
            
            # 如果是字典格式的GPIO数据（从GpioDevice.read_gpio返回）
            if isinstance(data, dict):
                if pin is not None and pin in data:
                    raw_value = data[pin]
                else:
                    # 如果没有指定pin，尝试使用key作为pin
                    key = dp_config.get('key')
                    if key and key in data:
                        raw_value = data[key]
                    else:
                        # 获取所有引脚的状态作为位图
                        raw_value = self.__convert_to_bitmap(data)
            
            # 如果是字节数据（向后兼容）
            elif isinstance(data, (int, bytes)):
                raw_value = data if isinstance(data, int) else int.from_bytes(data, byteorder='big')
            
            else:
                self._log.warning("Unsupported GPIO data format: %s", type(data))
                return None
            
            # 类型转换
            if data_type == 'int':
                return int(raw_value)
            elif data_type == 'float' or data_type == 'double':
                return float(raw_value)
            elif data_type == 'bool':
                return bool(raw_value)
            elif data_type == 'string':
                return str(raw_value)
            elif data_type == 'hex':
                return hex(int(raw_value))
            elif data_type == 'binary':
                return bin(int(raw_value))
            else:
                return raw_value
                
        except Exception as e:
            self._log.error("Error converting GPIO value for pin %s: %s", dp_config.get('pin'), e)
            return None

    def __convert_to_bitmap(self, gpio_data):
        """Convert GPIO pin states to bitmap"""
        bitmap = 0
        for pin, value in gpio_data.items():
            if isinstance(pin, int) and isinstance(value, int):
                # 使用引脚号作为位位置（简化处理）
                bit_position = pin % 32  # 限制在32位内
                if value:
                    bitmap |= (1 << bit_position)
        return bitmap

    @staticmethod
    def __convert_value_to_type(data, dp_config):
        """Legacy method for backward compatibility"""
        data_type = dp_config.get('type', 'int')
        
        if isinstance(data, dict):
            # Convert dict to int (bitmap)
            value = 0
            for pin, state in data.items():
                if isinstance(pin, int) and state:
                    value |= (1 << (pin % 8))
            data_for_conversion = value
        else:
            data_for_conversion = data

        if data_type == 'string':
            return str(data_for_conversion)
        elif data_type == 'int':
            return int(data_for_conversion)
        elif data_type == 'float' or data_type == 'double':
            return float(data_for_conversion)
        elif data_type == 'bool':
            return bool(data_for_conversion)
        elif data_type == 'hex':
            return hex(int(data_for_conversion))
        else:
            return data_for_conversion

    @staticmethod
    def __convert_datapoint_key(key, dp_config, device_report_strategy, logger):
        """Convert key to datapoint key"""
        return TBUtility.convert_key_to_datapoint_key(key, device_report_strategy, dp_config, logger)


# Backward compatibility alias
CustomGpioUplinkCoverter = GpioUplinkConverter
CustomGpioUplinkConverter = GpioUplinkConverter
