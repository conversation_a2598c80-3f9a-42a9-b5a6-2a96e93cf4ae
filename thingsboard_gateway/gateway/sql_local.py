# coding=utf-8

import sys
import os
current_path = os.path.dirname(__file__)
sys.path.append(current_path)

import os
import sqlite3
import random
import string
from sql_config import DB_FILE_DIR, DB_TABLE_NAME as TABLE


def hash_password():
    random_string = ''.join(random.choices(string.ascii_letters + string.digits, k=32))
    return random_string


class SQLiteDB:
    def __init__(self, db_name):
        self.conn = sqlite3.connect(db_name)
        self.cursor = self.conn.cursor()

    def is_table_exist(self, table):
        # 检查表是否存在
        self.cursor.execute(f"SELECT name FROM sqlite_master WHERE name='{table}'")
        table_exists = self.cursor.fetchone()
        return table_exists

    def is_data_exist(self, table, name):
        self.cursor.execute(f"SELECT * FROM {table} WHERE name = '{name}'")
        data_exists = self.cursor.fetchone()
        return data_exists

    def is_token_exist(self, table, token):
        self.cursor.execute(f"SELECT * FROM {table} WHERE token = '{token}'")
        data_exists = self.cursor.fetchone()
        return data_exists

    def create_table(self, table):
        # 创建一个表
        self.cursor.execute('CREATE TABLE ' + table + ' ( name TEXT NOT NULL, last_time TEXT NOT NULL, status INTEGER NOT NULL )')
        # 提交更改
        self.conn.commit()

    def create_user_table(self, table):
        # 创建一个表
        self.cursor.execute('CREATE TABLE ' + table + ' ( name TEXT NOT NULL, password TEXT NOT NULL, time_stamp INTEGER NOT NULL, token TEXT NOT NULL)')
        # 提交更改
        self.conn.commit()

    def insert(self, table, columns, values):
        # 构建插入语句
        placeholders = ', '.join('?' * len(values))
        columns_str = ', '.join(columns)
        sql = f'INSERT INTO {table} ({columns_str}) VALUES ({placeholders})'
        self.cursor.execute(sql, values)
        self.conn.commit()

    def delete(self, table, condition):
        # 构建删除语句
        sql = f'DELETE FROM {table} WHERE {condition}'
        self.cursor.execute(sql)
        self.conn.commit()

    def select(self, table, columns, condition=None):
        # 构建查询语句
        columns_str = ', '.join(columns)
        sql = f'SELECT {columns_str} FROM {table}'
        if condition:
            sql += f' WHERE {condition}'
        self.cursor.execute(sql)
        return self.cursor.fetchall()

    def update(self, table, set_values, condition):
        # 构建更新语句
        set_str = ', '.join([f'{column} = ?' for column in set_values.keys()])
        sql = f'UPDATE {table} SET {set_str} WHERE {condition}'
        self.cursor.execute(sql, list(set_values.values()))
        self.conn.commit()

    def close(self):
        # 关闭数据库连接
        self.conn.close()


class SqlOperation:
    @staticmethod
    def add_data(name, last_time, status_):
        db = SQLiteDB(DB_FILE_DIR)
        table = TABLE
        # 使用示例
        db_exist = db.is_table_exist(table=table)
        if db_exist:
            pass
        else:
            db.create_table(table=table)

        data_exist = db.is_data_exist(table=table, name=name)
        if data_exist:
            db.update(table, {'last_time': str(last_time)}, f'name = "{name}"')
        else:
            db.insert(table, ['name', 'last_time', 'status'], [name, str(last_time), status_])

        db.close()

    @staticmethod
    def select_data(pwd_=DB_FILE_DIR, logger_=None):
        data_list = []
        try:
            db = SQLiteDB(pwd_)
            table = TABLE
            # 使用示例
            db_exist = db.is_table_exist(table=table)
            if db_exist:
                pass
            else:
                db.create_table(table=table)

            rows = db.select(table, ['name', 'last_time', 'status'])
            for row in rows:
                if row[0].startswith("."):
                    pass
                else:
                    data_list.append({"name": row[0], "last_time": row[1]})

            db.close()
        except Exception as e:
            if logger_:
                logger_.info(f"读取 {pwd_} 失败, 失败原因: {str(e)}")
        return data_list


class UserOperation:
    @staticmethod
    def is_data_exist(db_name, table_name, condition_name):
        db = SQLiteDB(db_name)
        db_exist = db.is_table_exist(table=table_name)
        if db_exist:
            pass
        else:
            db.create_user_table(table=table_name)
        data_exist = db.is_data_exist(table=table_name, name=condition_name)
        db.close()
        return data_exist

    @staticmethod
    def is_token_exist(db_name, table_name, condition_name):
        db = SQLiteDB(db_name)
        db_exist = db.is_table_exist(table=table_name)
        if db_exist:
            pass
        else:
            db.create_user_table(table=table_name)
        data_exist = db.is_token_exist(table=table_name, token=condition_name)
        db.close()
        return data_exist

    @staticmethod
    def register_user(db_name, table_name, condition_name, password, time_stamp):
        db = SQLiteDB(db_name)
        db_exist = db.is_table_exist(table=table_name)
        if db_exist:
            pass
        else:
            db.create_user_table(table=table_name)
        db.insert(table_name,
                  ['name', 'password', 'time_stamp', 'token'],
                  [condition_name, str(password), time_stamp, ''])
        db.close()

    @staticmethod
    def login_user(db_name, table_name, condition_name):
        db = SQLiteDB(db_name)
        db_exist = db.is_table_exist(table=table_name)
        if db_exist:
            pass
        else:
            db.create_user_table(table=table_name)
        db.update(table_name, {'token': hash_password()}, f'name = "{condition_name}"')
        rows = db.select(table_name, ['password', 'token'], condition=f"name='{condition_name}'")
        db.close()
        return rows[0]

    @staticmethod
    def modified_user(db_name, table_name, condition_name, password):
        db = SQLiteDB(db_name)
        db_exist = db.is_table_exist(table=table_name)
        if db_exist:
            pass
        else:
            db.create_user_table(table=table_name)

        db.update(table_name, {'password': str(password)}, f'name = "{condition_name}"')
        db.close()

    @staticmethod
    def login_out_user(db_name, table_name, condition_token):
        db = SQLiteDB(db_name)
        db_exist = db.is_table_exist(table=table_name)
        if db_exist:
            pass
        else:
            db.create_user_table(table=table_name)

        db.update(table_name, {'token': ''}, f'token = "{condition_token}"')
        db.close()

    @staticmethod
    def detail_by_token(db_name, table_name, condition_token):
        db = SQLiteDB(db_name)
        db_exist = db.is_table_exist(table=table_name)
        if db_exist:
            pass
        else:
            db.create_user_table(table=table_name)
        rows = db.select(table_name, ['name', 'password'], condition=f"token='{condition_token}'")
        db.close()
        return rows[0]

    @staticmethod
    def password_by_user(db_name, table_name, condition_name):
        db = SQLiteDB(db_name)
        db_exist = db.is_table_exist(table=table_name)
        if db_exist:
            pass
        else:
            db.create_user_table(table=table_name)
        rows = db.select(table_name, ['password', 'token'], condition=f"name='{condition_name}'")
        db.close()
        return rows[0]