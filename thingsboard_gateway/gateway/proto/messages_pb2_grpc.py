# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import thingsboard_gateway.gateway.proto.messages_pb2 as messages__pb2


class TBGatewayProtoServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.stream = channel.unary_unary(
                '/messages.TBGatewayProtoService/stream',
                request_serializer=messages__pb2.FromConnectorMessage.SerializeToString,
                response_deserializer=messages__pb2.FromServiceMessage.FromString,
                )


class TBGatewayProtoServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def stream(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TBGatewayProtoServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'stream': grpc.unary_unary_rpc_method_handler(
                    servicer.stream,
                    request_deserializer=messages__pb2.FromConnectorMessage.FromString,
                    response_serializer=messages__pb2.FromServiceMessage.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'messages.TBGatewayProtoService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class TBGatewayProtoService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def stream(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/messages.TBGatewayProtoService/stream',
            messages__pb2.FromConnectorMessage.SerializeToString,
            messages__pb2.FromServiceMessage.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
