# coding=utf-8

import sys
import os
current_path = os.path.dirname(__file__)
sys.path.append(current_path)

import json
import time
import re
import inspect
import logging
import logging.handlers
import uvicorn
from pydantic import BaseModel
from fastapi import FastAPI
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import threading
import subprocess
import netifaces
from gateway.sql_local import SqlOperation, UserOperation
from gateway.sql_config import DB_FILE_DIR, USER_DB_DIR, USER_TABLE_NAME, USER_NAME_DEAFULT, USER_PWD_DEAFULT, CONNECTOR_TYPES_FILE
from tb_utility.tb_gateway_remote_configurator import RemoteConfigurator

# Path to default connector configurations
DEFAULT_CONFIGS_DIR = os.path.join(current_path, 'default_configs')

uri = "/gateway/v1"
DIR_NAME = "config"
configs_dir = os.path.join(current_path, DIR_NAME)
if not os.path.exists(configs_dir):
    os.makedirs(configs_dir)
current_log_path = os.path.join(current_path, 'logs')


class LogUtils:
    def __init__(self, log_name=None, log_path=None):
        caller_filename = inspect.stack()[1].filename.split('\\')[-1]
        self.log_name = log_name if log_name else caller_filename
        self.logfile_path = log_path if log_path else current_log_path
        if not os.path.exists(self.logfile_path):
            os.makedirs(self.logfile_path)

        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(level=logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s')
        self.log_name_path = os.path.join(self.logfile_path, "%s" % (self.log_name + ".log"))
        self.handler = logging.handlers.RotatingFileHandler(self.log_name_path, maxBytes=1024 * 1024, backupCount=5,
                                                          encoding="utf-8")
        self.handler.setLevel(logging.INFO)
        self.handler.setFormatter(formatter)

        if self.logger.hasHandlers():
            self.logger.handlers.clear()

        self.logger.addHandler(self.handler)
        self.console = logging.StreamHandler()
        self.console.setLevel(logging.INFO)
        self.console.setFormatter(formatter)
        self.logger.addHandler(self.console)
        self.console.close()

    def get_log(self):
        return self.logger


app = FastAPI()
# 创建一个锁对象
file_lock = threading.Lock()
logger_ = LogUtils("operation_python").get_log()


# ###############################################################################
# 重启 thingsboard-gateway
def kill_software():
    with open(os.path.join(current_path, 'kill_gateway.sh'), 'r') as file:
        script_content = file.read()
    result = subprocess.run(['bash', '-c', script_content], capture_output=True, text=True)


# 触发远程配置更新
def trigger_remote_configuration_update(config_data, file_name):
    """
    触发远程配置更新操作

    Args:
        config_data: 配置数据字典
        file_name: 配置文件名
    """
    try:
        if file_name == "tb_gateway.json":
            # 主配置文件更新 - 触发通用配置更新
            logger_.info("触发主配置文件远程更新")
            RemoteConfigurator.RECEIVED_UPDATE_QUEUE.put({
                'general_configuration': config_data.get('thingsboard', {})
            })

            # 如果包含存储配置，单独触发存储配置更新
            if 'storage' in config_data:
                RemoteConfigurator.RECEIVED_UPDATE_QUEUE.put({
                    'storage_configuration': config_data['storage']
                })

            # 如果包含GRPC配置，单独触发GRPC配置更新
            if 'grpc' in config_data:
                RemoteConfigurator.RECEIVED_UPDATE_QUEUE.put({
                    'grpc_configuration': config_data['grpc']
                })

            # 如果包含连接器配置，触发活动连接器更新
            if 'connectors' in config_data:
                RemoteConfigurator.RECEIVED_UPDATE_QUEUE.put({
                    'active_connectors': config_data['connectors']
                })

        elif file_name.endswith('.json') and file_name != "tb_gateway.json":
            # 连接器配置文件更新
            logger_.info(f"触发连接器配置文件远程更新: {file_name}")

            # 尝试从主配置文件中获取连接器信息
            connector_info = _get_connector_info_from_main_config(file_name)

            # 为连接器配置创建更新数据结构
            connector_config = {
                'configuration': file_name,
                'configurationJson': config_data,
                'ts': int(time.time() * 1000)
            }

            # 如果找到了连接器信息，添加到配置中
            if connector_info:
                connector_config.update(connector_info)
                connector_name = connector_info.get('name', file_name.replace('.json', '').replace('_', ' ').title())
            else:
                # 使用文件名生成连接器名称
                connector_name = file_name.replace('.json', '').replace('_', ' ').title()

            RemoteConfigurator.RECEIVED_UPDATE_QUEUE.put({
                connector_name: connector_config
            })

        logger_.info(f"远程配置更新已触发: {file_name}")

    except Exception as e:
        logger_.error(f"触发远程配置更新失败: {str(e)}")
        # 不抛出异常，避免影响文件写入操作


def _get_connector_info_from_main_config(config_file_name):
    """
    从主配置文件中获取连接器信息

    Args:
        config_file_name: 连接器配置文件名

    Returns:
        dict: 连接器信息字典，包含name、type等字段
    """
    try:
        main_config_path = os.path.join(current_path, 'config', 'tb_gateway.json')
        if not os.path.exists(main_config_path):
            logger_.warning(f"主配置文件不存在: {main_config_path}")
            return None

        with open(main_config_path, 'r', encoding='utf-8') as f:
            main_config = json.load(f)

        connectors = main_config.get('connectors', [])
        for connector in connectors:
            if connector.get('configuration') == config_file_name:
                return {
                    'name': connector.get('name', ''),
                    'type': connector.get('type', ''),
                    'id': connector.get('id', ''),
                    'logLevel': connector.get('logLevel', 'INFO'),
                    'enableRemoteLogging': connector.get('enableRemoteLogging', False)
                }

        logger_.warning(f"在主配置文件中未找到配置文件 {config_file_name} 对应的连接器信息")
        return None

    except Exception as e:
        logger_.error(f"读取主配置文件失败: {str(e)}")
        return None

# ###############################################################################
# 用户认证
# 从数据库中读取令牌
security = HTTPBearer()


def get_secret_token(token):
    is_token_exist = \
        UserOperation.is_token_exist(db_name=USER_DB_DIR, table_name=USER_TABLE_NAME, condition_name=token)
    if is_token_exist:
        return token
    return None


# 定义一个依赖函数来验证令牌
async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if credentials.scheme != "Bearer":
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication scheme")
    try:
        token = credentials.credentials
    except ValueError:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token format")

    expected_token_exist = get_secret_token(token)
    if not expected_token_exist:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    return token


# Models
class NetworkConfig(BaseModel):
    interface: str
    address: str
    netmask: str
    gateway: str
    dns1: str
    dns2: str = None
class RemoteAssistConfig(BaseModel):
    vkey: str

class RemoteAssistStatus(BaseModel):
    status: str = "stopped"  # running/stopped


class NetworkStatus(BaseModel):
    task_id: str


@app.get(f"{uri}/network/status")
async def get_network_status(task_id: str, token: str = Depends(verify_token)):
    """获取网络配置任务状态"""
    try:
        status_file = f"/tmp/network_status_{task_id}"
        if not os.path.exists(status_file):
            return {"msg": "success", "data": {"status": "running"}}

        with open(status_file, 'r') as f:
            status = f.read().strip()

        if status == "success":
            subprocess.run(['rm', status_file])
            return {"msg": "success", "data": {"status": "success"}}
        elif status == "failed":
            subprocess.run(['rm', status_file])
            return {"msg": "fail", "data": "网络配置失败,已自动回滚"}

        return {"msg": "success", "data": {"status": "running"}}
    except Exception as e:
        logger_.error(f"获取配置状态失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


class DataSaveItem(BaseModel):
    file_name: str
    file_text: str
    file_type: str = "string"
    dirname: str = DIR_NAME


class FileStatusItem(BaseModel):
    file_list: list


class DeviceConnectorsItem(BaseModel):
    device_list: list


class UserLoginItem(BaseModel):
    name: str
    password: str
    new_password: str = None
    confirm_password: str = None


class UserLoginOutItem(BaseModel):
    token: str


# ###############################################################################
# 网络配置相关API
@app.get(f"{uri}/network/interfaces")
async def get_network_interfaces(token: str = Depends(verify_token)):
    """获取所有网络接口及其状态"""
    try:
        interfaces = netifaces.interfaces()
        # 过滤掉lo接口
        interfaces = [iface for iface in interfaces if iface != 'lo']

        result = []
        for iface in interfaces:
            status = {}
            try:
                # 读取接口状态(up/down)
                with open(f"/sys/class/net/{iface}/operstate") as f:
                    status['state'] = f.read().strip()

                # 读取carrier状态
                with open(f"/sys/class/net/{iface}/carrier") as f:
                    status['carrier'] = f.read().strip() == "1"

                # 读取速率信息
                try:
                    with open(f"/sys/class/net/{iface}/speed") as f:
                        speed = f.read().strip()
                        status['speed'] = f"{speed}Mbps" if speed.isdigit() else "未知"
                except:
                    status['speed'] = "未知"

            except Exception as e:
                logger_.error(f"获取{iface}状态失败: {str(e)}")
                status = {
                    'state': 'unknown',
                    'carrier': False,
                    'speed': '未知'
                }

            result.append({
                'name': iface,
                'status': status
            })

        return {"msg": "success", "data": result}
    except Exception as e:
        logger_.error(f"获取网络接口失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


@app.get(f"{uri}/network/config")
async def get_network_config(interface: str, token: str = Depends(verify_token)):
    """获取指定网络接口的配置"""
    try:
        # 获取IP地址信息
        addrs = netifaces.ifaddresses(interface)
        ipv4_info = addrs.get(netifaces.AF_INET, [{}])[0]

        # 获取默认网关
        gateways = netifaces.gateways()
        default_gateway = gateways.get('default', {}).get(netifaces.AF_INET, (None, None))[0]

        # 获取DNS服务器
        dns_servers = []
        try:
            with open('/etc/resolv.conf', 'r') as f:
                for line in f:
                    if line.startswith('nameserver'):
                        dns_servers.append(line.split()[1])
        except:
            pass

        config = {
            "interface": interface,
            "address": ipv4_info.get('addr', ''),
            "netmask": ipv4_info.get('netmask', ''),
            "gateway": default_gateway or '',
            "dns1": dns_servers[0] if len(dns_servers) > 0 else '',
            "dns2": dns_servers[1] if len(dns_servers) > 1 else ''
        }

        return {"msg": "success", "data": config}
    except Exception as e:
        logger_.error(f"获取网络配置失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


@app.post(f"{uri}/network/config")
async def set_network_config(config: NetworkConfig, token: str = Depends(verify_token)):
    """设置网络配置"""
    try:
        # 生成任务ID
        task_id = str(int(time.time()))
        status_file = f"/tmp/network_status_{task_id}"

        # 创建状态文件,标记为正在执行
        with open(status_file, 'w') as f:
            f.write("running")

        # 1. 生成主配置文件内容
        main_interfaces_config = """# This file describes the network interfaces available on your system
# and how to activate them. For more information, see interfaces(5).

source /etc/network/interfaces.d/*

# The loopback network interface
auto lo
iface lo inet loopback
"""
        # 写入主配置文件
        with open('/tmp/interfaces.new', 'w') as f:
            f.write(main_interfaces_config)

        # 2. 生成网卡配置内容
        interface_config = f"""# Network interface {config.interface}
auto {config.interface}
iface {config.interface} inet static
    address {config.address}
    netmask {config.netmask}
    gateway {config.gateway}
"""
        # 写入网卡配置文件
        with open(f'/tmp/interface_{config.interface}.new', 'w') as f:
            f.write(interface_config)

        # 3. 配置DNS
        resolv_conf = f"nameserver {config.dns1}\n"
        if config.dns2:
            resolv_conf += f"nameserver {config.dns2}\n"

        with open('/tmp/resolv.conf.new', 'w') as f:
            f.write(resolv_conf)

        # 4. 创建应用配置脚本
        apply_script = f"""#!/bin/bash

# 更新状态为正在配置
echo "applying" > {status_file}

# 确保目录存在
sudo mkdir -p /etc/network/interfaces.d

# 备份当前配置
sudo cp /etc/network/interfaces /etc/network/interfaces.bak
sudo cp /etc/network/interfaces.d/interface_{config.interface} /etc/network/interfaces.d/interface_{config.interface}.bak 2>/dev/null || true
sudo cp /etc/resolv.conf /etc/resolv.conf.bak

# 应用新主配置文件
sudo mv /tmp/interfaces.new /etc/network/interfaces

# 应用网卡配置
sudo mv /tmp/interface_{config.interface}.new /etc/network/interfaces.d/interface_{config.interface}

# 应用DNS配置
sudo mv /tmp/resolv.conf.new /etc/resolv.conf

# 重启网卡
sudo ifdown {config.interface} 2>/dev/null || true
sudo ifup {config.interface}

# 等待网卡启动
sleep 5

# 测试网络连通性
if ping -c 1 {config.gateway} > /dev/null 2>&1; then
    # 配置成功,清理备份
    sudo rm -f /etc/network/interfaces.bak
    sudo rm -f /etc/network/interfaces.d/interface_{config.interface}.bak
    sudo rm -f /etc/resolv.conf.bak
    echo "success" > {status_file}
else
    # 配置失败,恢复配置
    sudo mv /etc/network/interfaces.bak /etc/network/interfaces
    [ -f /etc/network/interfaces.d/interface_{config.interface}.bak ] && sudo mv /etc/network/interfaces.d/interface_{config.interface}.bak /etc/network/interfaces.d/interface_{config.interface}
    sudo mv /etc/resolv.conf.bak /etc/resolv.conf
    sudo ifdown {config.interface} 2>/dev/null || true
    sudo ifup {config.interface}
    echo "failed" > {status_file}
fi
"""
        # 写入并执行脚本
        with open('/tmp/apply_network.sh', 'w') as f:
            f.write(apply_script)
        subprocess.run(['chmod', '+x', '/tmp/apply_network.sh'])

        # 在后台执行配置脚本
        subprocess.Popen(['sudo', '/tmp/apply_network.sh'],
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL,
                        preexec_fn=os.setpgrp)

        # 返回任务ID供前端轮询状态
        return {"msg": "success", "data": {"task_id": task_id}}

    except Exception as e:
        logger_.error(f"设置网络配置失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


# ###############################################################################
# 远程协助相关API
@app.post(f"{uri}/remote_assist/config")
async def set_remote_assist_config(config: RemoteAssistConfig, token: str = Depends(verify_token)):
    """设置远程协助vkey配置"""
    try:
        # vkey配置文件路径
        config_file = os.path.join(current_path, 'db', 'remote_assist.json')

        # 检查是否已经配置过
        if os.path.exists(config_file):
            return {"msg": "fail", "data": "vkey已配置,不可修改"}

        # 写入配置文件
        with open(config_file, 'w') as f:
            json.dump({"vkey": config.vkey}, f)

        return {"msg": "success", "data": "vkey配置成功"}
    except Exception as e:
        logger_.error(f"设置远程协助配置失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

@app.get(f"{uri}/remote_assist/config")
async def get_remote_assist_config(token: str = Depends(verify_token)):
    """获取远程协助vkey配置"""
    try:
        config_file = os.path.join(current_path, 'db', 'remote_assist.json')
        if not os.path.exists(config_file):
            return {"msg": "success", "data": {"vkey": "", "configured": False}}

        with open(config_file, 'r') as f:
            config = json.load(f)

        return {"msg": "success", "data": {"vkey": config["vkey"], "configured": True}}
    except Exception as e:
        logger_.error(f"获取远程协助配置失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

@app.post(f"{uri}/remote_assist/start")
async def start_remote_assist(token: str = Depends(verify_token)):
    """启动远程协助"""
    try:
        # 检查是否已配置vkey
        config_file = os.path.join(current_path, 'db', 'remote_assist.json')
        if not os.path.exists(config_file):
            return {"msg": "fail", "data": "请先配置vkey"}

        # 读取vkey配置
        with open(config_file, 'r') as f:
            config = json.load(f)

        # 检查是否已经在运行
        pid_file = "/tmp/remote_assist.pid"
        if os.path.exists(pid_file):
            return {"msg": "fail", "data": "远程协助已在运行"}

        # 启动npc客户端
        cmd = f"npc -server=www.iotcloud.top:8024 -vkey={config['vkey']} -type=tcp"
        process = subprocess.Popen(cmd.split(),
                                stdout=subprocess.DEVNULL,
                                stderr=subprocess.DEVNULL)

        # 保存进程ID
        with open(pid_file, 'w') as f:
            f.write(str(process.pid))

        return {"msg": "success", "data": "远程协助已启动"}
    except Exception as e:
        logger_.error(f"启动远程协助失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

@app.post(f"{uri}/remote_assist/stop")
async def stop_remote_assist(token: str = Depends(verify_token)):
    """停止远程协助"""
    try:
        pid_file = "/tmp/remote_assist.pid"
        if not os.path.exists(pid_file):
            return {"msg": "fail", "data": "远程协助未在运行"}

        # 读取进程ID
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())

        # 终止进程
        try:
            os.kill(pid, 9)
        except ProcessLookupError:
            pass

        # 删除pid文件
        os.remove(pid_file)

        return {"msg": "success", "data": "远程协助已停止"}
    except Exception as e:
        logger_.error(f"停止远程协助失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

@app.get(f"{uri}/remote_assist/status")
async def get_remote_assist_status(token: str = Depends(verify_token)):
    """获取远程协助状态"""
    try:
        pid_file = "/tmp/remote_assist.pid"
        status = "running" if os.path.exists(pid_file) else "stopped"
        return {"msg": "success", "data": {"status": status}}
    except Exception as e:
        logger_.error(f"获取远程协助状态失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

# ###############################################################################
# 原有API
# curl --location 'http://127.0.0.1:20400/znu/v1/read_filename'
@app.get(f"{uri}/read_filename")
async def read_filename_func(dirname=DIR_NAME, token: str = Depends(verify_token)):
    logger_.info(f"读取 {dirname} 下的第一级目录文件")
    filename_list = []
    try:
        files_dirname = os.path.join(current_path, dirname)
        for item in os.listdir(files_dirname):
            item_path = os.path.join(files_dirname, item)
            if os.path.isfile(item_path):
                if str(item).endswith(".json"):
                    filename_list.append(item)
        return {"msg": "success", "data": filename_list}
    except Exception as e:
        logger_.error(f"{dirname} 查询文件失败，失败原因:{str(e)}")
        return {"msg": "fail", "data": filename_list}


# curl --location 'http://127.0.0.1:20400/znu/v1/read_json?file_name=demo.json'
@app.get(f"{uri}/read_json")
async def read_json_func(file_name, dirname=DIR_NAME, token: str = Depends(verify_token)):
    logger_.info(f"{dirname} | 读取配置json {file_name}")
    data = {}
    try:
        files_dirname = os.path.join(current_path, dirname)
        with file_lock:
            with open(os.path.join(files_dirname, file_name), encoding="utf-8", mode="r") as fp:
                data = json.load(fp)
            return {"msg": "success", "data": data}
    except Exception as e:
        logger_.error(f"{dirname}/{file_name} 读取文件失败，失败原因:{str(e)}")
        return {"msg": "fail", "data": data}


# curl --location 'http://127.0.0.1:20400/znu/v1/delete_json?file_name=demo.json'
@app.get(f"{uri}/delete_json")
async def delete_json_func(file_name, dirname=DIR_NAME, token: str = Depends(verify_token)):
    logger_.info(f"{dirname} | 删除文件 {file_name}")
    try:
        files_dirname = os.path.join(current_path, dirname)
        os.remove(os.path.join(files_dirname, file_name))
        return {"msg": "success", "data": "delete success"}
    except Exception as e:
        logger_.error(f"{dirname}/{file_name} 读取文件失败，失败原因:{str(e)}")
        return {"msg": "fail", "data": "delete fail"}


# curl --location 'http://127.0.0.1:20400/znu/v1/write_json' \
# --header 'Content-Type: application/json' \
# --data '{
#     "file_name": "dddd",
#     "file_text": "{\"mmm\":\"3rfeefwef\"}"
# }'
@app.post(f"{uri}/write_json")
async def write_json_func(dataitem: DataSaveItem, token: str = Depends(verify_token)):
    file_name = dataitem.file_name
    if ".json" not in file_name:
        file_name = file_name + ".json"

    file_text = dataitem.file_text
    dirname = dataitem.dirname
    files_dirname = os.path.join(os.path.join(current_path, dirname), file_name)
    logger_.info(f"{dirname} | 写入文件 {file_name}")

    # 注意：连接器类型信息现在由前端通过 updateConnectorTypes API 单独管理
    # 这里不再从配置文件中读取 type 字段，因为配置文件中已经不包含 type 字段
    # 类型信息完全由 connector_types.json 文件管理

    if os.path.isfile(files_dirname):
        is_exist = True
    else:
        is_exist = False

    if is_exist:
        os.remove(files_dirname)
        logger_.info(f"正在删除 {file_name}")
    try:
        json_data = json.loads(file_text)
        with file_lock:
            with open(files_dirname, "w", encoding="utf-8") as fp:
                json.dump(json_data, fp, indent=4)

        # 触发远程配置更新
        trigger_remote_configuration_update(json_data, file_name)

        kill_software()
        return {"msg": "success", "data": json_data}
    except Exception as e:
        return {"msg": "fail", "data": str(e)}


@app.get(f"{uri}/file_status")
async def read_file_status(type_, connector_name=None, token: str = Depends(verify_token)):
    logger_.info(f"|single| 进入后台进行shell操作 {type_}, {connector_name}")
    software_cmd = 'tb-gateway-shell'
    software_process = subprocess.Popen(software_cmd, shell=True, stdin=subprocess.PIPE, stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE)
    if type_ == "gateway_s":
        command_to_send = 'gateway -s'
    elif type_ == "connector_l":
        command_to_send = 'connector -l'
    elif type_ == "connector_s":
        command_to_send = f'connector -s {connector_name}'
    else:
        command_to_send = None
        logger_.info(f"file_status 接口传参出错: {type_} | {connector_name}")

    if command_to_send:
        software_process.stdin.write(command_to_send.encode('utf-8'))
        software_process.stdin.close()
        # 读取输出
        output = software_process.stdout.read().decode('utf-8')
        if "not running" in output:
            return {"msg": "fail", "data": "服务未启动"}

        shell_text_list = output.split("\n")[5:]
        shell_text_list = [item for item in shell_text_list if item != '']
        shell_text_list = [item for item in shell_text_list if item != "(gateway) |> "]
        shell_text_list = [item.replace("(gateway) |> ", "") for item in shell_text_list]
        return {"msg": "success", "data": shell_text_list}
    else:
        return {"msg": "fail", "data": "传参错误,type_=gateway_s,connector_l,connector_s,|只有在connector_s时存在connector_name"}


@app.post(f"{uri}/file_status/list")
async def read_file_status_list(filestatusitem: FileStatusItem, token: str = Depends(verify_token)):
    file_list = filestatusitem.file_list
    logger_.info(f"|double| 进入后台进行shell操作")
    status_list = []
    for file in file_list:
        file = str(file)
        software_cmd = 'tb-gateway-shell'
        software_process = subprocess.Popen(software_cmd, shell=True, stdin=subprocess.PIPE, stdout=subprocess.PIPE,
                                          stderr=subprocess.PIPE)
        command_to_send = f'connector -s {file}'
        software_process.stdin.write(command_to_send.encode('utf-8'))
        software_process.stdin.close()
        output = software_process.stdout.read().decode('utf-8')
        if "not running" in output:
            return {"msg": "fail", "data": "服务未启动"}

        shell_text_list = output.split("\n")[5:]
        shell_text_list = [item for item in shell_text_list if item != '']
        shell_text_list = [item for item in shell_text_list if item != "(gateway) |> "]
        shell_text_list = [item.replace("(gateway) |> ", "") for item in shell_text_list]
        status_list.append({file: shell_text_list[0]})
    return {"msg": "success", "data": status_list}


@app.get(f"{uri}/file_lasttime/list")
async def read_file_time(token: str = Depends(verify_token)):
    logger_.info(f"读取本地数据库 {str(DB_FILE_DIR)} 存储操作")
    result = SqlOperation.select_data(pwd_=DB_FILE_DIR, logger_=logger_)
    return {"msg": "success", "data": result}


@app.get(f"{uri}/file_config/list")
async def read_file_config_list(token: str = Depends(verify_token)):
    connectors_list = []
    logger_.info(f"读取connector列表")
    json_path_ = os.path.join(current_path, 'config')
    with open(os.path.join(json_path_, "tb_gateway.json"), encoding="utf-8", mode="r") as fp:
        data = json.load(fp)
    try:
        connectors = data.get("connectors")
        for connector_ in connectors:
            connectors_list.append(connector_)
    except Exception as e:
        logger_.error(f"json 格式有问题")
    return {"msg": "success", "data": connectors_list}


@app.post(f"{uri}/devices/connectors")
async def read_device_connectors(devicelistitem: DeviceConnectorsItem, token: str = Depends(verify_token)):
    device_list = devicelistitem.device_list
    logger_.info(f"读取connector下的设备名称")
    logger_.info(f"|device| 进入后台进行shell操作")
    status_list = []
    for device_name in device_list:
        device_name = str(device_name)
        software_cmd = 'tb-gateway-shell'
        software_process = subprocess.Popen(software_cmd, shell=True, stdin=subprocess.PIPE, stdout=subprocess.PIPE,
                                          stderr=subprocess.PIPE)
        command_to_send = f'connector -a {device_name}'
        software_process.stdin.write(command_to_send.encode('utf-8'))
        software_process.stdin.close()
        output = software_process.stdout.read().decode('utf-8')
        if "not running" in output:
            return {"msg": "fail", "data": "服务未启动"}

        shell_text_list = output.split("\n")[5:]
        shell_text = "".join(shell_text_list)
        if "not found" in shell_text:
            status_list.append({device_name: []})
        else:
            shell_text_list = [item for item in shell_text_list if item != '']
            shell_text_list = [item for item in shell_text_list if item != "(gateway) |> "]
            shell_text_list = [item.replace("(gateway) |> ", "") for item in shell_text_list]
            status_list.append({device_name: shell_text_list})
    return {"msg": "success", "data": status_list}


# ###############################################################################
# @app.post(f"{uri}/user/register")
# async def user_register(useritem: UserLoginItem):
#     name = useritem.name
#     password = useritem.password
#     confirm_password = useritem.confirm_password
#     if password != confirm_password:
#         return {"msg": "fail", "data": "两次输入的密码不相同"}
#     logger_.info(f"{name}|{password}用户注册")

#     pattern = r'^[A-Za-z0-9]{4,12}$'
#     if not re.match(pattern, name):
#         return {"msg": "fail", "data": f"{name}名字不符合规范，只允许密码只包含字母和数字，并且长度在4到12个字符之间"}
#     is_name_exist = \
#         UserOperation.is_data_exist(db_name=USER_DB_DIR, table_name=USER_TABLE_NAME, condition_name=name)
#     if is_name_exist:
#         return {"mag": "fail", "data": f"不允许起相同的名字{name}"}
#     try:
#         if len(password) == 0:
#             return {"msg": "fail", "data": f"密码长度不能为0"}
#         else:
#             if re.match(pattern, password):
#                 UserOperation.register_user(db_name=USER_DB_DIR, table_name=USER_TABLE_NAME,
#                                           condition_name=name, password=password, time_stamp=int(time.time()))
#                 return {"msg": "success", "data": f"{name}创建成功"}
#             else:
#                 return {"msg": "fail", "data": f"{password}不符合规范，只允许密码只包含字母和数字，并且长度在4到12个字符之间"}
#     except Exception as e:
#         return {"mag": "fail", "data": f"创建用户失败: {str(e)}"}
#

@app.post(f"{uri}/user/login")
async def user_login(useritem: UserLoginItem):
    name = useritem.name
    password = useritem.password
    logger_.info(f"{name}|{password}用户登录")
    is_name_exist = \
        UserOperation.is_data_exist(db_name=USER_DB_DIR, table_name=USER_TABLE_NAME, condition_name=name)
    if not is_name_exist:
        return {"mag": "fail", "data": f"用户{name}不存在"}

    try:
        new_password = password[16:-16]
    except Exception as e:
        return {"msg": "fail", "data": "密码构建错误"}
    get_password = \
        UserOperation.login_user(db_name=USER_DB_DIR, table_name=USER_TABLE_NAME, condition_name=name)
    print(new_password)
    print(str(get_password))
    if new_password != get_password[0]:
        return {"msg": "fail", "data": "密码错误"}
    return {"msg": "success", "data": get_password[1]}


@app.post(f"{uri}/user/update_pwd")
async def user_update(useritem: UserLoginItem):
    name = useritem.name
    password = useritem.password
    new_password = useritem.new_password
    confirm_password = useritem.confirm_password
    get_password = UserOperation.password_by_user(db_name=USER_DB_DIR, table_name=USER_TABLE_NAME, condition_name=name)
    if password != get_password[0]:
        return {"msg": "fail", "data": "原密码输入错误"}
    if new_password != confirm_password:
        return {"msg": "fail", "data": "两次输入的密码不相同"}
    logger_.info(f"{name}| 用户账号密码修改")

    if len(new_password) == 0:
        return {"msg": "fail", "data": f"密码长度不能为0"}
    else:
        pattern = r'^[A-Za-z0-9]{4,12}$'
        if re.match(pattern, new_password):
            try:
                UserOperation.modified_user(db_name=USER_DB_DIR, table_name=USER_TABLE_NAME,
                                         condition_name=name, password=new_password)
                return {"msg": "success", "data": "修改密码成功"}
            except Exception as e:
                return {"msg": "fail", "data": "修改密码失败"}
        else:
            return {"msg": "fail", "data": f"{new_password}不符合规范，只允许密码只包含字母和数字，并且长度在4到12个字符之间"}


@app.get(f"{uri}/user/login_out")
async def user_login_out(token: str = Depends(verify_token)):
    logger_.info("用户登出")
    try:
        UserOperation.login_out_user(db_name=USER_DB_DIR, table_name=USER_TABLE_NAME, condition_token=token)
    except Exception as e:
        return {"msg": "fail", "data": f"登出失败: {str(e)}"}
    return {"msg": "success", "data": "登出成功"}


@app.get(f"{uri}/user/detail")
async def user_detail(token: str = Depends(verify_token)):
    logger_.info(f"用户查看详情")
    try:
        rows = UserOperation.detail_by_token(db_name=USER_DB_DIR, table_name=USER_TABLE_NAME, condition_token=token)
        return {"msg": "success", "data": {"name": rows[0], "password": rows[1]}}
    except Exception as e:
        return {"msg": "fail", "data": "获取用户详情失败"}


class UserDefault:
    @staticmethod
    def create_user():
        is_name_exist = \
            UserOperation.is_data_exist(db_name=USER_DB_DIR, table_name=USER_TABLE_NAME, condition_name=USER_NAME_DEAFULT)
        if is_name_exist:
            pass
        else:
            UserOperation.register_user(db_name=USER_DB_DIR, table_name=USER_TABLE_NAME,
                                        condition_name=USER_NAME_DEAFULT, password=USER_PWD_DEAFULT, time_stamp=int(time.time()))

# Create default admin user on startup
UserDefault.create_user()

# 添加新的路由处理连接器实例类型映射
@app.get(f"{uri}/connector_types")
async def get_connector_types(token: str = Depends(verify_token)):
    """获取连接器实例类型映射配置"""
    try:
        if not os.path.exists(CONNECTOR_TYPES_FILE):
            return {"msg": "success", "data": {"connector_instances": {}}}

        with open(CONNECTOR_TYPES_FILE, 'r') as f:
            config = json.load(f)

        return {"msg": "success", "data": config}
    except Exception as e:
        logger_.error(f"获取连接器类型配置失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

@app.post(f"{uri}/connector_types")
async def update_connector_types(data: dict, token: str = Depends(verify_token)):
    """更新连接器实例类型映射配置"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(CONNECTOR_TYPES_FILE), exist_ok=True)

        # 写入配置文件
        with open(CONNECTOR_TYPES_FILE, 'w') as f:
            json.dump(data, f, indent=4)

        return {"msg": "success", "data": "连接器类型配置更新成功"}
    except Exception as e:
        logger_.error(f"更新连接器类型配置失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

# 获取默认连接器配置
@app.get(f"{uri}/default_connector_config")
async def get_default_connector_config(connector_type: str, token: str = Depends(verify_token)):
    """获取指定类型的默认连接器配置"""
    try:
        # 检查连接器类型是否有效
        if connector_type not in ['mqtt', 'modbus', 'opcua', 'ble', 'request', 'can', 'bacnet',
                                 'odbc', 'rest', 'snmp', 'ftp', 'socket', 'xmpp', 'ocpp', 'gpio', 'serial', 'knx']:
            return {"msg": "fail", "data": f"不支持的连接器类型: {connector_type}"}

        # 构建默认配置文件路径
        config_file = os.path.join(DEFAULT_CONFIGS_DIR, f"{connector_type}.json")

        # 检查文件是否存在
        if not os.path.exists(config_file):
            return {"msg": "fail", "data": f"找不到默认配置文件: {connector_type}.json"}

        # 读取默认配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        return {"msg": "success", "data": config}
    except Exception as e:
        logger_.error(f"获取默认连接器配置失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

if __name__ == '__main__':
    uvicorn.run(app="front_interface:app", host="0.0.0.0", port=20400, workers=3)