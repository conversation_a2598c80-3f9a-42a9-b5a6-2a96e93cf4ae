{"name": "GPIO Connector Demo", "logLevel": "INFO", "enableRemoteLogging": false, "id": "87b5a8f4-82c1-4b5d-a9e3-f7c2d4e6a8b1", "uplinkQueueSize": 100000, "reportStrategy": {"type": "ON_CHANGE_OR_REPORT_PERIOD", "reportPeriod": 60000}, "devices": [{"name": "RaspberryPi GPIO Controller", "deviceName": "RaspberryPi GPIO Controller", "deviceType": "gpio_controller", "type": "gpio", "converter": "GpioUplinkConverter", "downlink_converter": "GpioDownlinkConverter", "gpioPins": [17, 18, 19, 20], "pinMode": "out", "pullUpDown": "off", "pollingPeriod": 1000, "telemetry": [{"key": "gpio17_state", "type": "bool", "pin": 17}, {"key": "gpio18_value", "type": "int", "pin": 18}, {"key": "all_pins_bitmap", "type": "hex"}], "attributes": [{"key": "gpio_mode", "type": "string", "pin": 17}, {"key": "device_info", "type": "string"}], "attributeUpdates": [{"attributeOnPlatform": "led_control", "pin": 17}, {"attributeOnPlatform": "relay_switch", "pin": 18}], "serverSideRpc": [{"method": "setGpioPin", "type": "int", "pin": 17, "withResponse": true, "responseTimeoutSec": 5}, {"method": "togglePin", "type": "bool", "pin": 18, "withResponse": false}, {"method": "readAllPins", "type": "string", "withResponse": true, "responseTimeoutSec": 3}]}, {"name": "Digital Input Module", "deviceName": "Digital Input Module", "deviceType": "digital_input", "type": "gpio", "converter": "GpioUplinkConverter", "gpioPins": [21, 22, 23, 24], "pinMode": "in", "pullUpDown": "up", "pollingPeriod": 500, "telemetry": [{"key": "button1_pressed", "type": "bool", "pin": 21}, {"key": "sensor2_triggered", "type": "bool", "pin": 22}, {"key": "door_switch", "type": "bool", "pin": 23}, {"key": "motion_detected", "type": "bool", "pin": 24}], "attributes": [{"key": "input_count", "type": "int"}, {"key": "last_trigger_time", "type": "string"}]}, {"name": "Mixed GPIO Device", "deviceName": "Mixed GPIO Device", "deviceType": "mixed_gpio", "type": "gpio", "converter": "GpioUplinkConverter", "downlink_converter": "GpioDownlinkConverter", "gpioPins": [25, 26, 27], "pinMode": "out", "pullUpDown": "off", "pollingPeriod": 2000, "telemetry": [{"key": "status_led", "type": "bool", "pin": 25}, {"key": "power_relay", "type": "bool", "pin": 26}], "attributes": [{"key": "device_status", "type": "string"}], "attributeUpdates": [{"attributeOnPlatform": "auto_mode", "pin": 27, "valueMapping": "0->manual,1->auto"}], "serverSideRpc": [{"method": "setStatusLed", "type": "bool", "pin": 25, "withResponse": true, "responseTimeoutSec": 2}, {"method": "togglePowerRelay", "type": "bool", "pin": 26, "withResponse": true, "responseTimeoutSec": 3}, {"method": "emergencyShutdown", "type": "string", "withResponse": true, "responseTimeoutSec": 10, "allowedPins": [25, 26, 27]}]}]}