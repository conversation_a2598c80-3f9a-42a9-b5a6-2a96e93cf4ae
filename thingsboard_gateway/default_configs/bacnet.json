{"application": {"objectName": "TB_gateway", "host": "0.0.0.0", "port": "47808", "mask": "24", "objectIdentifier": 599, "maxApduLengthAccepted": 1476, "segmentationSupported": "segmentedBoth", "vendorIdentifier": 15, "deviceDiscoveryTimeoutInSec": 5, "networkNumber": 0, "networkNumberQuality": "configured", "devicesDiscoverPeriodSeconds": 30}, "devices": [{"deviceInfo": {"deviceNameExpression": "BACnet Device ${objectName}", "deviceProfileExpression": "default", "deviceNameExpressionSource": "expression", "deviceProfileExpressionSource": "constant"}, "altResponsesAddresses": [], "host": "*************", "port": "47808", "mask": "24", "pollPeriod": 10000, "attributes": [{"key": "temperature", "objectType": "analogInput", "objectId": "1", "propertyId": "presentValue"}], "timeseries": [{"key": "state", "objectType": "binaryValue", "objectId": "1", "propertyId": "presentValue"}], "attributeUpdates": [{"key": "brightness", "objectType": "analogOutput", "objectId": "1", "propertyId": "presentValue"}], "serverSideRpc": [{"method": "set_state", "requestType": "writeProperty", "requestTimeout": 10000, "objectType": "binaryOutput", "objectId": "1", "propertyId": "presentValue"}, {"method": "get_state", "requestType": "readProperty", "requestTimeout": 10000, "objectType": "analogOutput", "objectId": "2", "propertyId": "presentValue"}]}]}