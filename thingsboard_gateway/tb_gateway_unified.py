#!/usr/bin/env python3
"""
ThingsBoard Gateway 统一启动器

将网关服务和管理API集成到同一个进程中，解决跨进程通信问题。

启动方式：
python tb_gateway_unified.py

功能：
1. 启动 TBGatewayService（网关核心服务）
2. 启动 FastAPI 管理接口（front_interface）
3. 在同一进程中运行，可以直接访问网关服务实例
4. 解决 RemoteConfigurator 实例获取问题
"""

import sys
import os
import threading
import time
import uvicorn
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from gateway.tb_gateway_service import TBGatewayService
from gateway.constants import DEV_MODE_PARAMETER_NAME, TB_GW_DEV_DEBUG_SERVER_PORT
from tb_utility.tb_utility import TBUtility

# 全局变量存储网关服务实例
gateway_service_instance = None
gateway_service_lock = threading.Lock()

def get_gateway_service():
    """
    获取网关服务实例
    
    Returns:
        TBGatewayService: 网关服务实例
    """
    global gateway_service_instance
    with gateway_service_lock:
        return gateway_service_instance

def set_gateway_service(service):
    """
    设置网关服务实例
    
    Args:
        service: TBGatewayService 实例
    """
    global gateway_service_instance
    with gateway_service_lock:
        gateway_service_instance = service

def start_gateway_service(config_path):
    """
    启动网关服务
    
    Args:
        config_path: 配置文件路径
    """
    try:
        print("🚀 启动网关服务...")
        
        # 创建网关服务实例
        gateway = TBGatewayService(config_path)
        
        # 存储到全局变量
        set_gateway_service(gateway)
        
        print("✅ 网关服务启动成功")
        
        # 保持网关服务运行
        while not gateway.stopped:
            time.sleep(1)
            
    except Exception as e:
        print(f"❌ 网关服务启动失败: {str(e)}")
        raise

def start_management_api():
    """
    启动管理API服务
    """
    try:
        print("🌐 启动管理API服务...")
        
        # 等待网关服务初始化完成
        max_wait = 30  # 最多等待30秒
        wait_count = 0
        
        while gateway_service_instance is None and wait_count < max_wait:
            time.sleep(1)
            wait_count += 1
        
        if gateway_service_instance is None:
            print("⚠️ 警告：网关服务未完全初始化，管理API将使用备用方案")
        else:
            print("✅ 网关服务已初始化，管理API可以直接访问")
        
        # 导入并启动 FastAPI 应用
        from front_interface import app
        
        # 启动 uvicorn 服务器
        uvicorn.run(
            app=app,
            host="0.0.0.0",
            port=20400,
            log_level="info"
        )
        
    except Exception as e:
        print(f"❌ 管理API服务启动失败: {str(e)}")
        raise

def get_config_path():
    """
    获取配置文件路径
    
    Returns:
        str: 配置文件完整路径
    """
    # 默认配置路径
    default_config_path = current_dir / 'config'
    
    # 从环境变量获取配置路径
    config_path = os.environ.get("TB_GW_CONFIG_DIR", str(default_config_path))
    
    if not config_path.endswith(os.path.sep):
        config_path += os.path.sep
    
    return config_path + "tb_gateway.json"

def is_running_under_pycharm():
    """
    检查是否在 PyCharm 下运行
    
    Returns:
        bool: 如果在 PyCharm 下运行返回 True
    """
    return (
        "PYCHARM_HOSTED" in os.environ
        or "_pydevd_bundle" in sys.modules
    )

def run_debug_server():
    """
    启动调试服务器（开发模式）
    """
    try:
        import debugpy
    except ImportError:
        TBUtility.install_package("debugpy")
        import debugpy

    debugpy_port = int(os.environ.get("TB_GW_DEV_DEBUG_SERVER", TB_GW_DEV_DEBUG_SERVER_PORT))
    debugpy.listen(("0.0.0.0", debugpy_port))

def main():
    """
    主函数 - 统一启动网关服务和管理API
    """
    print("=" * 60)
    print("🎯 ThingsBoard Gateway 统一启动器")
    print("=" * 60)
    
    # 检查开发模式
    is_dev_mode = TBUtility.str_to_bool(os.environ.get(DEV_MODE_PARAMETER_NAME, 'false'))
    
    if is_dev_mode and not is_running_under_pycharm():
        run_debug_server()
    
    # 创建日志目录
    logs_dir = current_dir / "logs"
    if not logs_dir.exists():
        logs_dir.mkdir(exist_ok=True)
    
    # 获取配置文件路径
    config_path = get_config_path()
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        sys.exit(1)
    
    print(f"📁 配置文件路径: {config_path}")
    
    try:
        # 在单独线程中启动网关服务
        gateway_thread = threading.Thread(
            target=start_gateway_service,
            args=(config_path,),
            name="GatewayService",
            daemon=True
        )
        gateway_thread.start()
        
        # 等待网关服务启动
        print("⏳ 等待网关服务启动...")
        time.sleep(3)
        
        # 在主线程中启动管理API（这样可以接收 Ctrl+C 信号）
        start_management_api()
        
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号，正在关闭服务...")
        
        # 停止网关服务
        if gateway_service_instance:
            gateway_service_instance.stop()
        
        print("✅ 服务已停止")
        
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
